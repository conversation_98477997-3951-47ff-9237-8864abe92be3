{"time":"2025-08-11T16:10:23.761132+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-11T16:10:23.763732+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-11T16:10:23.763918+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/config.Load","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/config/load.go","line":85},"msg":"No providers configured"}
{"time":"2025-08-11T16:10:24.416855+02:00","level":"INFO","msg":"OK   20250424200609_initial.sql (1.04ms)"}
{"time":"2025-08-11T16:10:24.417267+02:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (386.17µs)"}
{"time":"2025-08-11T16:10:24.417566+02:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (279.79µs)"}
{"time":"2025-08-11T16:10:24.417859+02:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (281.42µs)"}
{"time":"2025-08-11T16:10:24.417867+02:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-11T16:10:24.419349+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-11T16:10:24.419606+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/app.New","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/app/app.go","line":97},"msg":"No agent configuration found"}
{"time":"2025-08-11T16:10:24.573722+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/Users/<USER>/go/pkg/mod/github.com/charmbracelet/crush@v0.4.0/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
