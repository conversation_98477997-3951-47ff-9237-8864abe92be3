#!/usr/bin/env python3
"""
Simple test server to verify Lang<PERSON>hain orchestration endpoints
"""
import json
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

app = FastAPI(title="Test LangChain Service", version="1.0.0")

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "service": "test-ai-orchestrator",
        "langchain": "mock-enabled"
    }

@app.post("/api/v1/orchestrate")
async def test_orchestrate(request_data: dict):
    """Mock orchestration endpoint"""
    return {
        "status": "success",
        "message": "Mock orchestration complete",
        "request": request_data.get("request", ""),
        "services": request_data.get("services", []),
        "user_id": request_data.get("user_id", ""),
        "results": {
            "Hello.cv": "Mock resume analysis complete",
            "YoinkUI": "Mock UI component generated"
        }
    }

@app.get("/api/v1/memory/{user_id}")
async def test_memory(user_id: str):
    """Mock memory endpoint"""
    return {
        "user_id": user_id,
        "conversations": ["Mock conversation 1", "Mock conversation 2"],
        "entities": {"skills": ["Python", "React"], "experience": "5 years"},
        "status": "mock-enabled"
    }

@app.post("/api/v1/workflow/execute") 
async def test_workflow(workflow_data: dict):
    """Mock workflow endpoint"""
    return {
        "status": "success", 
        "workflow_id": workflow_data.get("workflow_id"),
        "input": workflow_data.get("input"),
        "result": "Mock workflow execution complete"
    }

if __name__ == "__main__":
    print("🚀 Starting Test LangChain Service on http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)