{"hash": "1fe59d89", "configHash": "de7f49e8", "lockfileHash": "06264caa", "browserHash": "f057a286", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "42b6ae2c", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "6afe7033", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "281cdfdc", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "356a0108", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b3a27cf9", "needsInterop": true}, "@headlessui/react": {"src": "../../../../node_modules/@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "2d9ea103", "needsInterop": false}, "@heroicons/react/20/solid": {"src": "../../../../node_modules/@heroicons/react/20/solid/esm/index.js", "file": "@heroicons_react_20_solid.js", "fileHash": "89db62c8", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../../../node_modules/@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "646d291d", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../../../node_modules/@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "2e29132c", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "28be5f99", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "78662e07", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "b6e33761", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "f0386090", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a2ba9cbd", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1bca0468", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "2936a4a2", "needsInterop": false}, "reactflow": {"src": "../../../../node_modules/reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "7db271a7", "needsInterop": false}, "socket.io-client": {"src": "../../../../node_modules/socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "2d3ec685", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c543f899", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/index.js", "file": "zod.js", "fileHash": "f83c01dd", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "bfdc8d88", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "102327f8", "needsInterop": false}}, "chunks": {"chunk-AUYUPDD5": {"file": "chunk-AUYUPDD5.js"}, "chunk-43WDTZ6H": {"file": "chunk-43WDTZ6H.js"}, "chunk-PKJM3SIU": {"file": "chunk-PKJM3SIU.js"}, "chunk-J4UD52JM": {"file": "chunk-J4UD52JM.js"}, "chunk-X5JZSKBF": {"file": "chunk-X5JZSKBF.js"}, "chunk-HXA6O6EE": {"file": "chunk-HXA6O6EE.js"}}}