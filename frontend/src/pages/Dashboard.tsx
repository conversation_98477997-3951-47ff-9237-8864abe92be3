import React, { useEffect, useState, useRef } from 'react';
import useAuthStore from '@/stores/authStore';
import { useNavigate } from 'react-router-dom';
import { motion, useMotionValue, useTransform, useSpring } from 'framer-motion';

console.log('🌟 AURORA BENTO DASHBOARD LOADED - v2.0');

// Service data for the 8 AI services with Bento grid layout properties
const services = [
  {
    id: 'velian',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Advanced AI analytics and insights platform with comprehensive data visualization, predictive modeling, and real-time monitoring capabilities.',
    color: '#8b5cf6', // purple
    stats: { requests: 15234, uptime: '99.9%' },
    // Primary service - largest card
    gridSpan: { col: 2, row: 2 },
    minHeight: '300px',
    priority: 'primary'
  },
  {
    id: 'zeroentropy',
    name: 'ZeroEntropy', 
    description: 'Quantum-powered optimization engine for complex computational problems',
    color: '#06b6d4', // cyan
    stats: { requests: 12847, uptime: '99.8%' },
    // Primary service - wide card
    gridSpan: { col: 2, row: 1 },
    minHeight: '300px',
    priority: 'primary'
  },
  {
    id: 'hello_cv',
    name: 'Hello.cv',
    description: 'Resume parser and CV analysis with AI-powered insights',
    color: '#10b981', // emerald
    stats: { requests: 8921, uptime: '99.7%' },
    // Secondary service
    gridSpan: { col: 2, row: 1 },
    minHeight: '200px',
    priority: 'secondary'
  },
  {
    id: 'yoink_ui',
    name: 'YoinkUI',
    description: 'UI/UX design automation tools',
    color: '#f59e0b', // amber
    stats: { requests: 6743, uptime: '99.9%' },
    // Secondary service
    gridSpan: { col: 2, row: 1 },
    minHeight: '200px',
    priority: 'secondary'
  },
  {
    id: 'clueso',
    name: 'Clueso',
    description: 'Intelligent search and discovery',
    color: '#ef4444', // red
    stats: { requests: 4521, uptime: '98.5%' },
    // Compact service
    gridSpan: { col: 1, row: 1 },
    minHeight: '150px',
    priority: 'compact'
  },
  {
    id: 'permut',
    name: 'Permut',
    description: 'Permutation and combination engine',
    color: '#8b5cf6', // indigo
    stats: { requests: 3892, uptime: '99.6%' },
    // Compact service
    gridSpan: { col: 1, row: 1 },
    minHeight: '150px',
    priority: 'compact'
  },
  {
    id: 'intervo',
    name: 'Intervo',
    description: 'Interactive voice AI assistant',
    color: '#14b8a6', // teal
    stats: { requests: 5678, uptime: '99.8%' },
    // Compact service
    gridSpan: { col: 1, row: 1 },
    minHeight: '150px',
    priority: 'compact'
  },
  {
    id: 'pixelesq',
    name: 'Pixelesq',
    description: 'Image processing and generation',
    color: '#ec4899', // pink
    stats: { requests: 7234, uptime: '99.7%' },
    // Compact service
    gridSpan: { col: 1, row: 1 },
    minHeight: '150px',
    priority: 'compact'
  }
];

function Dashboard() {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  
  console.log('🎯 AURORA DASHBOARD RENDERING, user:', user);
  
  // Mouse tracking for parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
        const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
        setMousePosition({ x, y });
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);
  
  // Inject Aurora CSS animations
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes aurora-1 {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.7; }
        33% { transform: translate(30vw, -30vh) rotate(120deg); opacity: 0.4; }
        66% { transform: translate(-20vw, 20vh) rotate(240deg); opacity: 0.8; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.7; }
      }
      
      @keyframes aurora-2 {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.5; }
        33% { transform: translate(-25vw, 25vh) rotate(120deg); opacity: 0.9; }
        66% { transform: translate(25vw, -15vh) rotate(240deg); opacity: 0.3; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.5; }
      }
      
      @keyframes aurora-3 {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.6; }
        33% { transform: translate(20vw, 30vh) rotate(120deg); opacity: 0.4; }
        66% { transform: translate(-30vw, -20vh) rotate(240deg); opacity: 0.8; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.6; }
      }
      
      @keyframes gradient-shift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      
      .card-float-1 { animation: float 6s ease-in-out infinite; }
      .card-float-2 { animation: float 7s ease-in-out infinite 1s; }
      .card-float-3 { animation: float 8s ease-in-out infinite 2s; }
      .card-float-4 { animation: float 6.5s ease-in-out infinite 1.5s; }
      .card-float-5 { animation: float 7.5s ease-in-out infinite 0.5s; }
      .card-float-6 { animation: float 8.5s ease-in-out infinite 2.5s; }
      .card-float-7 { animation: float 6.2s ease-in-out infinite 3s; }
      .card-float-8 { animation: float 7.8s ease-in-out infinite 3.5s; }
      
      /* Responsive Bento Grid */
      @media (max-width: 1024px) {
        .bento-grid {
          grid-template-columns: repeat(4, 1fr) !important;
        }
      }
      
      @media (max-width: 768px) {
        .bento-grid {
          grid-template-columns: repeat(2, 1fr) !important;
          gap: 1rem !important;
        }
        .bento-card {
          grid-column: span 1 !important;
          grid-row: span 1 !important;
          min-height: 150px !important;
        }
      }
      
      @media (max-width: 480px) {
        .bento-grid {
          grid-template-columns: 1fr !important;
          gap: 0.75rem !important;
        }
        .bento-card {
          min-height: 120px !important;
        }
      }
      
      /* Responsive welcome section */
      @media (max-width: 768px) {
        .welcome-title {
          font-size: 2rem !important;
        }
        .welcome-subtitle {
          font-size: 1rem !important;
        }
        .container-padding {
          padding: 1rem !important;
        }
      }
      
      @media (max-width: 480px) {
        .welcome-title {
          font-size: 1.5rem !important;
        }
        .welcome-subtitle {
          font-size: 0.9rem !important;
        }
      }
      
      /* Platform overview responsive */
      @media (max-width: 768px) {
        .platform-overview {
          margin-top: 2rem !important;
          padding: 1.5rem !important;
        }
        .platform-stats {
          grid-template-columns: repeat(2, 1fr) !important;
          gap: 1rem !important;
        }
      }
      
      @media (max-width: 480px) {
        .platform-overview {
          margin-top: 1.5rem !important;
          padding: 1rem !important;
        }
        .platform-stats {
          grid-template-columns: 1fr !important;
        }
      }
    `;
    document.head.appendChild(styleElement);
    
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  
  const handleServiceClick = (serviceId: string) => {
    navigate(`/services/${serviceId}`);
  };

  return (
    <div 
      className="container-padding"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
        color: 'white',
        margin: '-2.5rem -2rem -2.5rem -2rem',
        padding: '2rem',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Aurora Background Blobs */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 0
        }}
      >
        {/* Aurora Blob 1 - Purple */}
        <div
          style={{
            position: 'absolute',
            top: '20%',
            left: '10%',
            width: '500px',
            height: '500px',
            background: 'radial-gradient(circle, #8B5CF6 0%, #8B5CF6 20%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(40px)',
            animation: 'aurora-1 20s ease-in-out infinite',
            opacity: 0.7
          }}
        />
        
        {/* Aurora Blob 2 - Blue */}
        <div
          style={{
            position: 'absolute',
            top: '60%',
            right: '10%',
            width: '400px',
            height: '400px',
            background: 'radial-gradient(circle, #3B82F6 0%, #3B82F6 25%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(40px)',
            animation: 'aurora-2 25s ease-in-out infinite',
            opacity: 0.5
          }}
        />
        
        {/* Aurora Blob 3 - Pink */}
        <div
          style={{
            position: 'absolute',
            top: '40%',
            left: '60%',
            width: '350px',
            height: '350px',
            background: 'radial-gradient(circle, #EC4899 0%, #EC4899 30%, transparent 70%)',
            borderRadius: '50%',
            filter: 'blur(40px)',
            animation: 'aurora-3 18s ease-in-out infinite',
            opacity: 0.6
          }}
        />
      </div>
      
      {/* Content Container */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Welcome Section */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 
            className="welcome-title"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #667eea 100%)',
              backgroundSize: '300% 300%',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontSize: '3rem',
              fontWeight: 'bold',
              marginBottom: '1rem',
              animation: 'gradient-shift 6s ease-in-out infinite',
              textShadow: '0 0 30px rgba(102, 126, 234, 0.5)'
            }}
          >
            Welcome back, {user?.first_name || 'User'}!
          </h1>
          <p className="welcome-subtitle" style={{ color: '#94a3b8', fontSize: '1.2rem' }}>
            Your AI services are running smoothly
          </p>
        </div>

        {/* Bento Grid Services */}
        <div 
          ref={containerRef}
          className="bento-grid"
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(6, 1fr)',
            gridAutoRows: 'minmax(150px, auto)',
            gap: '1.5rem',
            maxWidth: '1200px',
            margin: '0 auto',
            perspective: '1000px',
            transformStyle: 'preserve-3d'
          }}
        >
          {services.map((service, index) => {
            const rotateX = mousePosition.y * 10;
            const rotateY = -mousePosition.x * 10;
            
            return (
              <motion.div
                key={service.id}
                className={`bento-card card-float-${index + 1}`}
                onClick={() => handleServiceClick(service.id)}
                whileHover={{ 
                  scale: service.priority === 'primary' ? 1.015 : service.priority === 'secondary' ? 1.02 : 1.05,
                  rotateY: 8,
                  rotateX: 8,
                  z: 50
                }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ 
                  opacity: 1, 
                  y: 0,
                  rotateX: rotateX * 0.3,
                  rotateY: rotateY * 0.3
                }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: service.priority === 'primary' ? '2rem' : service.priority === 'secondary' ? '1.5rem' : '1rem',
                  cursor: 'pointer',
                  transformStyle: 'preserve-3d',
                  boxShadow: `
                    0 ${20 + Math.abs(rotateX)}px ${60 + Math.abs(rotateX * 2)}px rgba(0, 0, 0, 0.4),
                    0 0 30px ${service.color}20,
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `,
                  // Bento grid spanning
                  gridColumn: `span ${service.gridSpan.col}`,
                  gridRow: `span ${service.gridSpan.row}`,
                  minHeight: service.minHeight,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}
              >
              {/* Card Content */}
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                {/* Service Header */}
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                  <div
                    style={{
                      width: service.priority === 'primary' ? '16px' : '12px',
                      height: service.priority === 'primary' ? '16px' : '12px',
                      backgroundColor: service.color,
                      borderRadius: '50%',
                      marginRight: '0.75rem',
                      boxShadow: `0 0 ${service.priority === 'primary' ? '15px' : '10px'} ${service.color}40`
                    }}
                  />
                  <h3 style={{ 
                    fontSize: service.priority === 'primary' ? '1.75rem' : service.priority === 'secondary' ? '1.25rem' : '1rem', 
                    fontWeight: '600', 
                    margin: 0,
                    color: 'white'
                  }}>
                    {service.name}
                  </h3>
                </div>

                {/* Service Description - show more for larger cards */}
                <p style={{ 
                  color: '#cbd5e1', 
                  fontSize: service.priority === 'primary' ? '1rem' : service.priority === 'secondary' ? '0.9rem' : '0.8rem',
                  marginBottom: service.priority === 'compact' ? '0.5rem' : '1rem',
                  lineHeight: '1.4',
                  flex: 1
                }}>
                  {service.description}
                </p>

                {/* Additional content for primary services */}
                {service.priority === 'primary' && (
                  <div style={{
                    padding: '1rem',
                    background: 'rgba(255, 255, 255, 0.03)',
                    borderRadius: '8px',
                    marginBottom: '1rem',
                    border: `1px solid ${service.color}20`
                  }}>
                    <div style={{ fontSize: '0.8rem', color: '#94a3b8', marginBottom: '0.5rem' }}>
                      Recent Activity
                    </div>
                    <div style={{ display: 'flex', gap: '1rem' }}>
                      <div>
                        <div style={{ color: service.color, fontWeight: '600' }}>+12%</div>
                        <div style={{ fontSize: '0.7rem', color: '#94a3b8' }}>vs last week</div>
                      </div>
                      <div>
                        <div style={{ color: '#22c55e', fontWeight: '600' }}>0 errors</div>
                        <div style={{ fontSize: '0.7rem', color: '#94a3b8' }}>24h period</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Service Stats */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: 'auto',
                paddingTop: service.priority === 'compact' ? '0.5rem' : '1rem'
              }}>
                <div>
                  <div style={{ 
                    color: service.color, 
                    fontWeight: '600',
                    fontSize: service.priority === 'primary' ? '1.2rem' : service.priority === 'secondary' ? '1.1rem' : '1rem'
                  }}>
                    {service.stats.requests.toLocaleString()}
                  </div>
                  <div style={{ 
                    color: '#94a3b8', 
                    fontSize: service.priority === 'compact' ? '0.7rem' : '0.8rem'
                  }}>
                    requests
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ 
                    color: service.stats.uptime === '99.9%' ? '#22c55e' : '#f59e0b',
                    fontWeight: '600',
                    fontSize: service.priority === 'primary' ? '1.2rem' : service.priority === 'secondary' ? '1.1rem' : '1rem'
                  }}>
                    {service.stats.uptime}
                  </div>
                  <div style={{ 
                    color: '#94a3b8', 
                    fontSize: service.priority === 'compact' ? '0.7rem' : '0.8rem'
                  }}>
                    uptime
                  </div>
                </div>
              </div>
            </motion.div>
            );
          })}
        </div>

        {/* Platform Overview */}
        <div 
          className="platform-overview"
          style={{
            marginTop: '3rem',
            padding: '2rem',
            background: 'rgba(255, 255, 255, 0.03)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.08)',
            borderRadius: '16px',
            maxWidth: '1200px',
            margin: '3rem auto 0',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
          }}
        >
          <h2 style={{ 
            fontSize: '1.5rem', 
            fontWeight: '600', 
            marginBottom: '1rem',
            textAlign: 'center'
          }}>
            Platform Overview
          </h2>
          <div 
            className="platform-stats"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: '2rem',
              textAlign: 'center'
            }}
          >
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>8</div>
              <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Active Services</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#22c55e' }}>64.2K</div>
              <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Total Requests</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#8b5cf6' }}>47ms</div>
              <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Avg Response</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>99.7%</div>
              <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Overall Uptime</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;