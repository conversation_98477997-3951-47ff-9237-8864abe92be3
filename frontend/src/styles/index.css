@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Component styles */
@layer components {
  /* Button variations */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300 dark:bg-secondary-800 dark:text-secondary-100 dark:hover:bg-secondary-700;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 active:bg-warning-800;
  }
  
  .btn-error {
    @apply bg-error-600 text-white hover:bg-error-700 active:bg-error-800;
  }
  
  .btn-ghost {
    @apply bg-transparent text-secondary-700 hover:bg-secondary-100 active:bg-secondary-200 dark:text-secondary-300 dark:hover:bg-secondary-800;
  }
  
  .btn-outline {
    @apply border border-secondary-300 bg-transparent text-secondary-700 hover:bg-secondary-50 active:bg-secondary-100 dark:border-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-800;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-soft;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
  }
  
  /* Status indicators */
  .status-active {
    @apply bg-success-100 text-success-800 border-success-200 dark:bg-success-900 dark:text-success-200 dark:border-success-800;
  }
  
  .status-inactive {
    @apply bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700;
  }
  
  .status-error {
    @apply bg-error-100 text-error-800 border-error-200 dark:bg-error-900 dark:text-error-200 dark:border-error-800;
  }
  
  .status-warning {
    @apply bg-warning-100 text-warning-800 border-warning-200 dark:bg-warning-900 dark:text-warning-200 dark:border-warning-800;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .form-error {
    @apply text-sm text-error-600 dark:text-error-400 mt-1;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100;
  }
  
  .nav-link-inactive {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-100;
  }
  
  /* Service-specific styles */
  .service-velian {
    @apply bg-gradient-to-r from-purple-500 to-purple-600;
  }
  
  .service-zeroentropy {
    @apply bg-gradient-to-r from-cyan-500 to-cyan-600;
  }
  
  .service-hello-cv {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
  }
  
  .service-yoink-ui {
    @apply bg-gradient-to-r from-amber-500 to-amber-600;
  }
  
  .service-clueso {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }
  
  .service-permut {
    @apply bg-gradient-to-r from-violet-500 to-violet-600;
  }
  
  .service-intervo {
    @apply bg-gradient-to-r from-sky-500 to-sky-600;
  }
  
  .service-pixelesq {
    @apply bg-gradient-to-r from-pink-500 to-pink-600;
  }
}

/* Utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-800/20;
  }
  
  .glass-strong {
    @apply backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border border-white/30 dark:border-gray-800/30;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}